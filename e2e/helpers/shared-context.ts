import type { Page, ElectronApplication } from 'playwright';
import { resetObsidianUI } from './plugin-setup';
import * as fs from 'fs';
import * as path from 'path';
import { randomUUID } from 'crypto';

/**
 * Shared context for all e2e tests
 * Provides access to the global Electron instance with worker isolation
 */
export interface SharedTestContext {
  page: Page;
  electronApp: ElectronApplication;
  testId: string;
  vaultPath: string;
  dataPath: string;
  workerId: string;
}

// Map of worker-specific contexts for parallel execution
const workerContexts = new Map<string, SharedTestContext>();

/**
 * Get the current worker ID for test isolation
 * Uses process.env.VITEST_WORKER_ID or falls back to process.pid
 */
function getWorkerId(): string {
  // Vitest sets VITEST_WORKER_ID for each worker process
  const workerId = process.env.VITEST_WORKER_ID ||
    process.env.VITEST_POOL_ID ||
    process.pid.toString();
  return `worker-${workerId}`;
}

/**
 * Get the shared test context with worker isolation
 * Creates isolated vault and data directories per worker for parallel execution
 */
export async function getSharedTestContext(): Promise<SharedTestContext> {
  const workerId = getWorkerId();

  // Check if we already have a context for this worker
  const existingContext = workerContexts.get(workerId);
  if (existingContext) {
    return existingContext;
  }

  console.log(`🔧 Creating isolated test environment for ${workerId}`);

  // Create isolated environment for this worker
  const { testId, vaultPath, dataPath } = await createIsolatedTestEnvironment(workerId);

  // Always create a separate Electron instance for each worker
  // This ensures true isolation for parallel execution
  let electronApp: ElectronApplication;
  let page: Page;

  try {
    console.log(`🚀 Creating dedicated Electron instance for ${workerId}...`);
    const { setupObsidianElectron } = await import('./plugin-setup');
    const result = await setupObsidianElectron(vaultPath, dataPath);
    electronApp = result.electronApp;
    page = result.page;

    console.log(`✅ Created isolated Electron instance for ${workerId}`);
  } catch (error) {
    throw new Error(
      `Failed to create Electron instance for ${workerId}: ${error.message}. ` +
      'Make sure the setup completed successfully.'
    );
  }

  const context: SharedTestContext = {
    electronApp,
    page,
    testId,
    vaultPath,
    dataPath,
    workerId
  };

  // Store context for this worker
  workerContexts.set(workerId, context);

  return context;
}

/**
 * Reset UI state for the next test
 * This should be called in beforeEach hooks
 */
export async function resetSharedTestContext(): Promise<void> {
  const context = await getSharedTestContext();
  // Quick UI reset without extensive cleanup
  await context.page.keyboard.press('Escape');
  await context.page.keyboard.press('Escape');
  await new Promise(resolve => setTimeout(resolve, 100));
}

/**
 * Clean up test-specific state
 * This should be called in afterEach hooks
 */
export async function cleanupTestState(): Promise<void> {
  const context = await getSharedTestContext();

  // Only do full UI reset in afterEach, not beforeEach
  await resetObsidianUI(context.page);

  // Clear any test files from the worker's isolated articles directory
  const articlesDir = path.join(context.vaultPath, 'articles');
  if (fs.existsSync(articlesDir)) {
    const files = fs.readdirSync(articlesDir);
    for (const file of files) {
      if (file.includes('test-') && file.endsWith('.md')) {
        try {
          fs.unlinkSync(path.join(articlesDir, file));
        } catch (error) {
          console.log(`⚠️ Could not remove test file ${file}:`, error.message);
        }
      }
    }
  }
}

/**
 * Clean up all worker contexts and isolated environments
 * This should be called during global teardown
 */
export async function cleanupAllWorkerContexts(): Promise<void> {
  console.log('🧹 Cleaning up all worker contexts...');

  for (const [workerId, context] of workerContexts.entries()) {
    try {
      console.log(`🧹 Cleaning up ${workerId}...`);

      // Close Electron app if it's not the shared one
      const globalElectronApp = (global as any).__SHARED_ELECTRON_APP__;
      if (context.electronApp && context.electronApp !== globalElectronApp) {
        try {
          // Get the process PID before closing for force cleanup if needed
          const electronProcess = context.electronApp.process();
          const pid = electronProcess?.pid;

          if (context.page && !context.page.isClosed()) {
            await context.page.close();
          }

          // Try graceful close first
          await context.electronApp.close();
          console.log(`✅ Gracefully closed Electron app for ${workerId}`);

          // If we have a PID and the process is still running, force kill it
          if (pid) {
            try {
              process.kill(pid, 0); // Check if process still exists
              console.log(`🔪 Force killing remaining process ${pid} for ${workerId}`);
              process.kill(pid, 'SIGTERM');

              // Give it a moment to terminate
              await new Promise(resolve => setTimeout(resolve, 500));

              // Check if it's still running and force kill if needed
              try {
                process.kill(pid, 0);
                process.kill(pid, 'SIGKILL');
                console.log(`💀 Force killed process ${pid} for ${workerId}`);
              } catch {
                // Process already terminated
              }
            } catch {
              // Process already terminated or doesn't exist
            }
          }
        } catch (error) {
          console.log(`⚠️ Error closing Electron app for ${workerId}:`, error.message);
        }
      }

      // Clean up isolated environment
      await cleanupIsolatedTestEnvironment(context.testId);

    } catch (error) {
      console.log(`⚠️ Error cleaning up ${workerId}:`, error.message);
    }
  }

  workerContexts.clear();
  console.log('✅ All worker contexts cleaned up');
}

export async function executeCommand(context: SharedTestContext, command: string): Promise<void> {
  await context.page.keyboard.press('Meta+P');
  await context.page.waitForSelector('.prompt-input');
  await context.page.fill('.prompt-input', command);
  await context.page.keyboard.press('Enter');
}

/**
 * Wait for and check if a notice with specific text appears
 */
export async function waitForNotice(context: SharedTestContext, expectedText: string, timeout: number = 5000): Promise<boolean> {
  try {
    await context.page.waitForFunction(
      ({ text }) => {
        const noticeElements = document.querySelectorAll('.notice');
        const notices = Array.from(noticeElements).map(el => el.textContent || '');
        return notices.some(notice => notice.includes(text));
      },
      { text: expectedText },
      { timeout }
    );
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Wait for and verify that a notice with specific text appears using expect
 */
export async function expectNotice(context: SharedTestContext, expectedText: string, timeout: number = 5000): Promise<void> {
  const { expect } = await import('vitest');
  const noticeAppeared = await waitForNotice(context, expectedText, timeout);
  expect(noticeAppeared).toBe(true);
}

/**
 * Get post file information for testing
 */
export async function getPostFile(
  context: SharedTestContext,
  filename: string
): Promise<{ exists: boolean; title?: string; content?: string; path?: string }> {
  return await context.page.evaluate(
    async ({ filename }) => {
      const app = (window as any).app;

      const articlesPath = 'articles';
      const fullPath = `${articlesPath}/${filename}.md`;

      const file = app.vault.getAbstractFileByPath(fullPath);
      if (!file) {
        return { exists: false, path: fullPath };
      }

      const fileCache = app.metadataCache.getFileCache(file);
      const frontmatter = fileCache?.frontmatter || {};
      const fullContent = await app.vault.read(file);

      let bodyContent = fullContent;
      if (fileCache?.frontmatterPosition) {
        const frontmatterEnd = fileCache.frontmatterPosition.end;
        bodyContent = fullContent.slice(frontmatterEnd.offset).trim();
      }

      return {
        exists: true,
        title: frontmatter.title,
        content: bodyContent,
        path: fullPath
      };
    },
    { filename }
  );
}

/**
 * Check if a post file exists and optionally validate its properties using expect
 */
export async function expectPostFile(
  context: SharedTestContext,
  filename: string,
  options?: { title?: string; content?: RegExp }
): Promise<void> {
  const { expect } = await import('vitest');
  const fileData = await getPostFile(context, filename);

  expect(fileData.exists).toBe(true);

  if (options?.title) {
    expect(fileData.title).toBe(options.title);
  }

  if (options?.content) {
    expect(fileData.content).toMatch(options.content);
  }
}

/**
 * Create an isolated test environment with unique vault and data directories
 * @param workerId Optional worker ID for consistent naming across worker processes
 */
export async function createIsolatedTestEnvironment(workerId?: string): Promise<{ testId: string; vaultPath: string; dataPath: string }> {
  const testId = workerId || randomUUID();
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);
  const vaultPath = path.join(testDir, 'vault');
  const dataPath = path.join(testDir, 'data');

  // Ensure base test directory exists
  await fs.promises.mkdir(baseTestDir, { recursive: true });
  await fs.promises.mkdir(testDir, { recursive: true });

  // Copy pristine vault to isolated vault
  const pristineVaultPath = path.resolve('./tests/vault/Test.pristine');
  if (fs.existsSync(pristineVaultPath)) {
    await copyDirectory(pristineVaultPath, vaultPath);
  } else {
    throw new Error('Pristine vault not found at tests/vault/Test.pristine');
  }

  // Copy pristine data directory to isolated data directory
  const pristineDataPath = path.resolve('./e2e/obsidian-data.pristine');
  if (fs.existsSync(pristineDataPath)) {
    await copyDirectory(pristineDataPath, dataPath);

    // Update obsidian.json to point to the isolated vault path
    const obsidianJsonPath = path.join(dataPath, 'obsidian.json');
    if (fs.existsSync(obsidianJsonPath)) {
      try {
        const obsidianConfig = JSON.parse(await fs.promises.readFile(obsidianJsonPath, 'utf8'));

        // Update all vault paths to point to the isolated vault
        if (obsidianConfig.vaults) {
          for (const vaultId in obsidianConfig.vaults) {
            obsidianConfig.vaults[vaultId].path = vaultPath;
          }
        }

        await fs.promises.writeFile(obsidianJsonPath, JSON.stringify(obsidianConfig, null, 2));
        console.log(`📝 Updated obsidian.json vault path to: ${vaultPath}`);
      } catch (error) {
        console.warn(`⚠️ Failed to update obsidian.json vault path: ${error.message}`);
      }
    }
  } else {
    // Create minimal data directory if pristine doesn't exist
    await fs.promises.mkdir(dataPath, { recursive: true });
  }

  return { testId, vaultPath, dataPath };
}

/**
 * Clean up isolated test environment
 */
export async function cleanupIsolatedTestEnvironment(testId: string): Promise<void> {
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);

  if (fs.existsSync(testDir)) {
    try {
      await fs.promises.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.log(`⚠️ Failed to cleanup test environment ${testId}:`, error.message);
    }
  }
}

/**
 * Recursively copy directory
 */
async function copyDirectory(src: string, dest: string): Promise<void> {
  await fs.promises.mkdir(dest, { recursive: true });

  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}
