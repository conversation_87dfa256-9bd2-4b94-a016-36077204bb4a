import type { Page } from 'playwright';

/**
 * Settings helpers for e2e tests
 * Provides robust functions for opening and interacting with Obsidian settings
 */

/**
 * Open Ghost Sync plugin settings
 * This function handles the complete flow: Settings -> Community plugins -> Ghost Sync
 */
export async function openGhostSyncSettings(page: Page): Promise<void> {
  // Quick cleanup - just close any open modals
  await page.keyboard.press('Escape');
  await page.keyboard.press('Escape');
  await page.waitForTimeout(100);

  // Open settings with Meta+Comma
  await page.keyboard.press('Meta+Comma');

  // Wait for settings modal to appear with shorter timeout
  await page.waitForSelector('.modal-container', { timeout: 8000 });

  // Shorter wait for modal to load
  await page.waitForTimeout(300);

  // Try to find and click "Community plugins" with multiple strategies
  const communityPluginsClicked = await clickCommunityPlugins(page);
  if (!communityPluginsClicked) {
    // If we can't find Community plugins, the settings modal might be corrupted
    // Try to recover by closing and reopening
    console.log('⚠️ Could not find Community plugins, attempting recovery...');
    await page.keyboard.press('Escape');
    await page.waitForTimeout(1000);
    await page.keyboard.press('Meta+Comma');
    await page.waitForSelector('.modal-container', { timeout: 15000 });
    await page.waitForTimeout(1000);

    const retryClicked = await clickCommunityPlugins(page);
    if (!retryClicked) {
      throw new Error('Could not find or click "Community plugins" in settings even after retry');
    }
  }

  // Wait for community plugins section to load
  await page.waitForTimeout(1000);

  // Try to find and click "Ghost Sync" with multiple strategies
  const ghostSyncClicked = await clickGhostSync(page);
  if (!ghostSyncClicked) {
    throw new Error('Could not find or click "Ghost Sync" plugin in community plugins');
  }

  // Wait for Ghost Sync settings to load
  await page.waitForSelector('text=Ghost Sync Settings', { timeout: 10000 });
  await page.waitForTimeout(500);
}

/**
 * Close settings modal
 */
export async function closeSettings(page: Page): Promise<void> {
  await page.keyboard.press('Escape');
  await page.waitForTimeout(500);
}



/**
 * Helper function to click "Community plugins" with multiple strategies
 */
async function clickCommunityPlugins(page: Page): Promise<boolean> {
  const strategies = [
    // Strategy 1: Direct text match
    async () => {
      const element = await page.$('text=Community plugins');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 2: Look for tab or button containing the text
    async () => {
      const element = await page.$('[role="tab"]:has-text("Community plugins")');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 3: Look for nav button or setting item
    async () => {
      const element = await page.$('.nav-button:has-text("Community plugins"), .setting-item:has-text("Community plugins")');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 4: Look for any clickable element with the text
    async () => {
      const element = await page.$('button:has-text("Community plugins"), div:has-text("Community plugins")');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 5: Look for partial text matches
    async () => {
      const element = await page.$('text=/Community.*plugins/i');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 6: Use more flexible text matching within modal
    async () => {
      const clicked = await page.evaluate(() => {
        const modal = document.querySelector('.modal-container');
        if (!modal) return false;

        const elements = Array.from(modal.querySelectorAll('*'));
        for (const element of elements) {
          const text = element.textContent?.trim() || '';
          if (text.toLowerCase().includes('community') && text.toLowerCase().includes('plugins')) {
            const tagName = element.tagName.toLowerCase();
            if (['button', 'div', 'span', 'a'].includes(tagName) || element.getAttribute('role') === 'tab') {
              try {
                (element as HTMLElement).click();
                return true;
              } catch (error) {
                // Continue to next element if click fails
              }
            }
          }
        }
        return false;
      });
      return clicked;
    }
  ];

  for (let i = 0; i < strategies.length; i++) {
    try {
      console.log(`Trying Community plugins strategy ${i + 1}...`);
      const success = await strategies[i]();
      if (success) {
        console.log(`Community plugins strategy ${i + 1} succeeded`);
        return true;
      }
    } catch (error) {
      // Continue to next strategy
      console.log(`Community plugins click strategy ${i + 1} failed: ${error.message}`);
    }

    // Wait a bit between strategies
    await page.waitForTimeout(500);
  }

  return false;
}

/**
 * Helper function to click "Ghost Sync" with multiple strategies
 */
async function clickGhostSync(page: Page): Promise<boolean> {
  const strategies = [
    // Strategy 1: Direct text match
    async () => {
      const element = await page.$('text=Ghost Sync');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 2: Look for plugin item containing the text
    async () => {
      const element = await page.$('.installed-plugin:has-text("Ghost Sync")');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 3: Look for any clickable element with the text
    async () => {
      const element = await page.$('button:has-text("Ghost Sync"), div:has-text("Ghost Sync")');
      if (element) {
        await element.click();
        return true;
      }
      return false;
    },

    // Strategy 4: Use more flexible text matching
    async () => {
      const elements = await page.$$('*');
      for (const element of elements) {
        const text = await element.textContent();
        if (text && text.includes('Ghost Sync')) {
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());
          if (['button', 'div', 'span', 'a'].includes(tagName)) {
            try {
              await element.click();
              return true;
            } catch (error) {
              // Continue to next element if click fails
            }
          }
        }
      }
      return false;
    }
  ];

  for (const strategy of strategies) {
    try {
      const success = await strategy();
      if (success) {
        return true;
      }
    } catch (error) {
      // Continue to next strategy
      console.log(`Ghost Sync click strategy failed: ${error.message}`);
    }

    // Wait a bit between strategies
    await page.waitForTimeout(500);
  }

  return false;
}

/**
 * Fill a setting input field by placeholder text
 */
export async function fillSettingInput(page: Page, placeholder: string, value: string): Promise<void> {
  const input = page.locator(`input[placeholder="${placeholder}"]`);
  await input.clear();
  await input.fill(value);
}

/**
 * Toggle a setting checkbox by label text
 */
export async function toggleSetting(page: Page, labelText: string, checked: boolean): Promise<void> {
  const checkbox = page.locator('input[type="checkbox"]').filter({ hasText: labelText }).or(
    page.locator('.checkbox-container').filter({ hasText: labelText }).locator('input').or(
      page.locator(`[data-setting="${labelText.toLowerCase()}"]`).or(
        page.locator(`text=${labelText}`).locator('..').locator('input[type="checkbox"]')
      )
    )
  );

  const exists = await checkbox.count() > 0;
  if (exists) {
    if (checked) {
      await checkbox.check();
    } else {
      await checkbox.uncheck();
    }
  }
}
