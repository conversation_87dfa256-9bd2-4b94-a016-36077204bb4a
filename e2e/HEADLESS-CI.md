# Headless CI Testing for Electron Apps

This document explains how the Ghost Sync plugin implements headless testing for CI environments, following [Electron's official documentation](https://www.electronjs.org/docs/latest/tutorial/testing-on-headless-ci).

## Overview

Electron applications require a display driver to function. In headless CI environments (like GitHub Actions, <PERSON>, <PERSON>), we need to use a virtual display driver to run Electron-based tests.

## Implementation

### 1. Virtual Display Setup

Our CI workflow uses `xvfb` (X Virtual Framebuffer) to provide a virtual display:

```bash
# Install xvfb
sudo apt-get install -y xvfb

# Run tests with virtual display
xvfb-run -a --server-args="-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96" npm run test:e2e:ci
```

### 2. CI Configuration

The `.github/workflows/e2e-tests.yml` workflow:

- Installs xvfb for virtual display
- Downloads and extracts Obsidian AppImage
- Sets up unpacked Obsidian for Playwright
- Runs tests with appropriate xvfb configuration

### 3. Test Configuration

The `vitest.playwright.config.mjs` automatically detects CI environment and adjusts:

- **Timeouts**: Longer timeouts in CI (3 minutes vs 2 minutes)
- **Concurrency**: Single worker in CI for stability
- **Reporting**: Verbose output and JUnit XML for CI

### 4. Electron Launch Configuration

The `plugin-setup.ts` adds CI-specific Electron flags:

```javascript
const electronApp = await electron.launch({
  args: [
    appPath,
    '--user-data-dir=' + userDataDir,
    // CI-specific flags for stability
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-software-rasterizer',
    // ... more flags
  ],
  timeout: isCI ? 45000 : 30000,
});
```

## Environment Variables

- `CI=true`: Automatically set by GitHub Actions, enables CI mode
- `E2E_HEADLESS=false`: Can be set locally to run tests in windowed mode
- `DISPLAY=:99`: Set by xvfb for virtual display

## Local Development

For local development, tests run in windowed mode by default:

```bash
# Windowed mode (default)
npm run test:e2e

# Force headless mode locally
E2E_HEADLESS=true npm run test:e2e

# Force windowed mode
npm run test:e2e:windowed
```

## Troubleshooting

### Common Issues

1. **Display not found**: Ensure xvfb is running and DISPLAY is set
2. **Timeout errors**: Increase timeout values in CI configuration
3. **Process cleanup**: The global teardown handles orphaned processes

### Debug CI Issues

1. Check xvfb is properly installed and running
2. Verify Obsidian AppImage extraction worked
3. Check test artifacts for detailed logs
4. Ensure proper process cleanup in teardown

## References

- [Electron Testing on Headless CI](https://www.electronjs.org/docs/latest/tutorial/testing-on-headless-ci)
- [Playwright Electron Documentation](https://playwright.dev/docs/api/class-electron)
- [xvfb Documentation](https://www.x.org/releases/X11R7.6/doc/man/man1/Xvfb.1.xhtml)
